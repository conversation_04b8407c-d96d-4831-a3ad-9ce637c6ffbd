{"name": "@tidepool/viz", "engines": {"node": "20.8.0"}, "packageManager": "yarn@3.6.4", "version": "1.48.0-web-3720-re-enable-one-min-cgm.1", "description": "Tidepool data visualization for diabetes device data.", "keywords": ["data visualization"], "main": "dist/index.js", "scripts": {"apidocs": "yarn jsdoc2md", "browser-tests": "NODE_ENV=test yarn karma start --browsers Chrome", "build": "NODE_ENV=production npm run clean && NODE_ENV=production yarn webpack --config package.config.js", "build-dev": "npm run clean && NODE_ENV=development  yarn webpack --config package.config.js --progress", "build-docs": "./update-gh-pages.sh", "build-storybooks": "yarn build-storybook -c storybook -o web/stories && yarn build-storybook -c storybookDatatypes -o web/diabetes-data-stories", "clean": "NODE_ENV=production yarn rimraf ./dist/*", "lint": "NODE_ENV=test yarn eslint src/ stories/ storiesDatatypes/ test/ *.js", "prepublishOnly": "NODE_ENV=production yarn rimraf ./node_modules && yarn install --immutable && npm test && NODE_ENV=production npm run build", "pretest": "NODE_ENV=test npm run lint", "start": "NODE_ENV=development yarn webpack --config package.config.js --watch --progress", "stories": "NODE_OPTIONS=\"--max_old_space_size=4096\" NODE_ENV=development yarn sb dev -c storybook -p 8083 --ci", "test": "TZ=UTC NODE_ENV=test yarn karma start", "test-watch": "TZ=UTC NODE_ENV=test DEV_TOOL=inline-source-map yarn karma start --no-single-run --reporters=mocha", "typestories": "NODE_ENV=development yarn sb dev -c storybookDatatypes -p 8082 --ci"}, "repository": {"type": "git", "url": "git+https://github.com/tidepool-org/viz.git"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/tidepool-org/viz/issues"}, "files": ["dist", "static-assets"], "homepage": "https://github.com/tidepool-org/viz#readme", "dependencies": {"bluebird": "3.7.2", "bows": "1.7.2", "browserify-zlib": "0.2.0", "buffer": "6.0.3", "classnames": "2.3.2", "crossfilter2": "1.5.4", "d3-array": "3.2.4", "d3-format": "3.1.0", "d3-scale": "4.0.2", "d3-shape": "3.2.0", "d3-time": "3.1.0", "d3-time-format": "4.1.0", "emotion": "11.0.0", "events": "3.3.0", "fastest-validator": "0.6.10", "gsap": "3.12.2", "i18next": "23.6.0", "intl": "1.2.5", "intl-pluralrules": "2.0.1", "lodash": "4.17.21", "memorystream": "0.3.1", "moment": "2.29.4", "moment-timezone": "0.5.43", "parse-svg-path": "0.1.2", "pdfkit": "0.13.0", "process": "0.11.10", "prop-types": "15.8.1", "react": "16.14.0", "react-clipboard.js": "2.0.16", "react-collapse": "5.1.1", "react-dimensions": "1.3.1", "react-dom": "16.14.0", "react-i18next": "13.3.1", "react-markdown": "8.0.7", "react-motion": "0.5.2", "react-redux": "8.1.3", "react-select": "5.7.7", "react-sizeme": "3.0.2", "react-transition-group-plus": "0.5.3", "readable-stream": "4.4.2", "reductio": "1.0.0", "redux": "4.2.1", "serialize-svg-path": "0.1.0", "sundial": "1.7.1", "svg-to-pdfkit": "0.1.8", "text-table": "0.2.0", "translate-svg-path": "0.0.1", "util": "0.12.5", "victory": "37.3.5", "victory-core": "37.3.5", "voilab-pdf-table": "0.5.1"}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.23.0", "@babel/eslint-parser": "7.22.15", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/plugin-transform-modules-commonjs": "7.23.0", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.23.2", "@babel/preset-react": "7.22.15", "@babel/runtime": "7.23.1", "@hot-loader/react-dom": "16.11.0", "@storybook/addon-essentials": "7.5.0", "@storybook/addon-knobs": "7.0.2", "@storybook/addons": "7.5.0", "@storybook/cli": "7.5.0", "@storybook/react": "7.5.0", "@storybook/react-webpack5": "7.5.0", "assert": "2.1.0", "babel-core": "7.0.0-bridge.0", "babel-loader": "9.1.3", "babel-plugin-istanbul": "6.1.1", "babel-preset-react-app": "10.0.1", "brfs": "2.0.2", "chai": "4.3.10", "chance": "1.1.11", "chromedriver": "135.0.2", "create-react-class": "15.7.0", "css-loader": "6.8.1", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.7", "eslint": "8.51.0", "eslint-config-airbnb": "19.0.4", "eslint-plugin-import": "2.28.1", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-lodash": "7.4.0", "eslint-plugin-moment-utc": "1.0.0", "eslint-plugin-react": "7.33.2", "jsdoc-to-markdown": "8.0.0", "json-loader": "0.5.7", "jsx-ast-utils": "3.3.5", "karma": "6.4.2", "karma-chai": "0.1.0", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-intl-shim": "1.0.3", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-sinon": "1.0.5", "karma-sourcemap-loader": "0.4.0", "karma-webpack": "5.0.0", "minimist": "1.2.8", "mocha": "10.2.0", "object-invariant-test-helper": "0.1.1", "optional": "0.1.4", "plotly.js-basic-dist-min": "2.26.2", "postcss": "8.4.31", "postcss-calc": "9.0.1", "postcss-custom-properties": "13.3.2", "postcss-loader": "7.3.3", "react-hot-loader": "4.13.1", "react-test-renderer": "16.14.0", "rimraf": "5.0.5", "sinon": "17.0.0", "style-loader": "3.3.3", "tidepool-standard-action": "0.1.1", "transform-loader": "0.2.4", "url-loader": "4.1.1", "webpack": "5.94.0", "webpack-cli": "5.1.4"}, "peerDependencies": {"babel-core": "6.x || ^7.0.0-bridge.0", "classnames": "2.x", "react": "16.x", "react-addons-update": "15.6.x", "react-dom": "16.x", "react-redux": "8.x", "redux": "4.x"}, "resolutions": {"highlight.js": "10.4.1", "jackspeak": "2.1.1", "lodash": "4.17.21", "prismjs": "1.29.0"}}