{"name": "tideline", "engines": {"node": "20.8.0"}, "packageManager": "yarn@3.6.4", "version": "1.33.0-web-3687-new-dosing-decision-extended-duration-props.1", "description": "Tidepool's timeline data visualization", "repository": {"type": "git", "url": "https://github.com/tidepool-org/tideline.git"}, "main": "js/index.js", "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "TZ=UTC NODE_ENV=test yarn karma start", "pretest": "NODE_ENV=test npm run lint", "browser-tests": "yarn karma start --browsers Chrome", "test-watch": "TZ=UTC NODE_ENV=test yarn karma start --no-single-run --reporters=mocha", "lint": "NODE_ENV=test yarn eslint js/ plugins/ test/ *.js"}, "dependencies": {"bows": "1.7.2", "classnames": "2.3.2", "crossfilter": "1.3.12", "d3": "3.5.17", "d3.chart": "0.3.0", "dompurify": "3.2.5", "duration-js": "4.0.0", "i18next": "23.6.0", "intl": "1.2.5", "lodash": "4.17.21", "moment": "2.29.4", "moment-timezone": "0.5.43", "react": "16.14.0", "react-dom": "16.14.0", "react-sizeme": "3.0.2", "sundial": "1.7.1"}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.23.0", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/plugin-transform-modules-commonjs": "7.23.0", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.22.20", "@babel/preset-react": "7.22.15", "@babel/runtime": "7.23.1", "@hot-loader/react-dom": "16.11.0", "autoprefixer": "10.4.16", "babel-core": "7.0.0-bridge.0", "babel-eslint": "9.0.0", "babel-loader": "9.1.3", "babel-plugin-istanbul": "6.1.1", "babel-plugin-rewire": "1.2.0", "babel-preset-react-app": "10.0.1", "chai": "4.3.10", "chromedriver": "135.0.2", "create-react-class": "15.7.0", "css-loader": "6.8.1", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.7", "eslint": "8.51.0", "eslint-config-airbnb": "19.0.4", "eslint-plugin-import": "2.28.1", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-lodash": "7.4.0", "eslint-plugin-mocha": "6.2.2", "eslint-plugin-moment-utc": "1.0.0", "eslint-plugin-react": "7.33.2", "file-loader": "6.2.0", "intl-pluralrules": "2.0.1", "jshint": "2.13.6", "jshint-stylish": "2.2.1", "json-loader": "0.5.7", "karma": "6.4.2", "karma-chai": "0.1.0", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-intl-shim": "1.0.3", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-sinon": "1.0.5", "karma-sourcemap-loader": "0.4.0", "karma-webpack": "5.0.0", "less": "4.2.0", "less-loader": "11.1.3", "mocha": "10.2.0", "node-polyfill-webpack-plugin": "2.0.1", "prop-types": "15.7.2", "rewire": "7.0.0", "script-loader": "0.7.2", "sinon": "17.0.0", "style-loader": "3.3.3", "url-loader": "4.1.1", "webpack": "5.89.0"}, "peerDependencies": {"babel-core": "6.x || 7.0.0-bridge.0", "lodash": "^4.17.21"}}