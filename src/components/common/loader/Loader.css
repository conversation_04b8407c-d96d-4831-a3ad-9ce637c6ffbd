.loader {
  display: flex;
  pointer-events: none;
}

.loaderDots {
  opacity: 1;
  transition: opacity .25s ease;
  display: flex;
  position: absolute;
  z-index: 10;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.loaderText {
  color: var(--text-high-contrast);
  font-size: 14px;
  display: flex;
  width: 100%;
  justify-content: center;
  position: absolute;
  top: calc(50% + 29px);
  left: 50%;
  transform: translate(-50%, -50%);
}

.loaderDots.overlay {
  background: radial-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.7));
}

.loaderDots.hidden {
  opacity: 0.01;
}

.loaderDot {
  margin: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--brand-purple-dark);
  transform: scale(0.5);
}

.loaderDot.animating {
  animation: scale-dot 1.2s ease-out infinite;
}

.loaderDot:nth-of-type(2) {
  animation-delay: 0.2s;
}

.loaderDot:nth-of-type(3) {
  animation-delay: 0.3s;
}

.loaderDot:nth-of-type(4) {
  animation-delay: 0.4s;
}

.loaderDot:nth-of-type(5) {
  animation-delay: 0.5s;
}

@keyframes scale-dot {
  0% {
    transform: scale(0.5);
  }
  25% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(0.5);
  }
}
