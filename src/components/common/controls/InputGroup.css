:global(.inputGroup-suffix__indicator-separator) {
  display: none !important;
}

:global(.inputGroup-suffix__input) {
  color: transparent;
}

:global(.inputGroup-suffix__control) {
  height: 24px;
  min-height: 24px !important;
  border: none !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

:global(.inputGroup-suffix__control--is-focused) {
  border: none !important;
  box-shadow: none !important;
  outline: 0 !important;
}

:global(.inputGroup-suffix__dropdown-indicator) {
  padding: 0 !important;
}

:global(.inputGroup-suffix__dropdown-indicator svg) {
  height: 14px;
}

:global(.inputGroup-suffix__value-container) {
  padding: 0 4px !important;
  line-height: 1;
}

.wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  composes: largeSize from '../../../styles/typography.css';
}

.inputs {
  display: flex;
  align-items: center;
  composes: smallSize from '../../../styles/typography.css';
}

.inputs > input:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border: 1px solid var(--stat--border);
  border-right: none;
  padding: 0 5px;
  height: 24px;
}

.inputs > input:first-child:focus {
  outline: 0;
}

.input-number {
  min-width: 50px;
  -moz-appearance: textfield;
}

.input-number::-webkit-outer-spin-button,
.input-number::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.suffix {
  min-width: 45px;
  border: 1px solid var(--input--border);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  height: 24px;
}

.suffixText {
  composes: suffix;
  display: flex;
  align-items: center;
  padding-left: 5px;
  min-width: 40px;
  color: var(--input--disabled);
}
