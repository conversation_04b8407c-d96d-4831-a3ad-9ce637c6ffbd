.row {
  composes: smallSize from '../../../styles/typography.css';
  line-height: 16px;
  max-width: 180px;
  min-width: 160px;
  font-weight: medium;
}

.message {
  composes: row;
  color: var(--stat--default);
}

.message p {
  margin: 0;
}

.message a {
  pointer-events: all;
}

.divider {
  height: 2px;
  margin: 10px -12px;
  background-color: var(--stat--default);
}

.hiddenDivider {
  height: 10px;
  background-color: transparent;
}
