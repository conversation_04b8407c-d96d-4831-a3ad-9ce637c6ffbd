/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2016, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

.tooltip {
  display: block;
  composes: defaultSize from '../../../styles/typography.css';
  background-color: #FFFFFF;
  border: solid;
  pointer-events: none;
  position: absolute;
  border-radius: 4px;
  z-index: 1000;
}

.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 12px;
  box-sizing: border-box;
}

.content span {
  width: 100%;
}

.title {
  composes: mediumContrastText from '../../../styles/typography.css';
  composes: content;
  background-color: var(--tooltip-title-bg);
}

.tail {
  width: 0px;
  height: 0px;
  border-style: solid;
  border-color: transparent;
  position: absolute;
}

:export {
  tooltipTitleBg: var(--tooltip-title-bg);
}
