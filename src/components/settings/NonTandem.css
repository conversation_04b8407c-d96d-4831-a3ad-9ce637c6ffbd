/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2016, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

.header {
  display: flex;
  justify-content: space-between;

  align-items: center;
  align-content: center;
}

.settingsContainer {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.settingsContainerLeftAligned {
  composes: settingsContainer;
  justify-content: flex-start;
  display: block;
}

.insulinSettingsInnerContainer {
  display: block;
  width: 100%;
}

.insulinSettingsInnerContainer > div {
  display: block;
  float: left;
  width: auto;
  padding-right: 10px;
}

.insulinSettingsInnerContainer > div:first-child {
  min-width: 25% !important;
}

.insulinSettingsInnerContainer > div:last-child {
  padding-right: 0;
  clear: right;
}

.basalSettingsContainer {
  flex-basis: 25%;
  margin-bottom: 15px;
}

.bolusSettingsContainer {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 75%;
  margin-bottom: 15px;
}

.bolusSettingsInnerContainer {
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  gap: 10px;
}

.categoryTitle {
  composes: mediumContrastText from '../../styles/typography.css';
  width: 100%;
  padding-bottom: 10px;
}

.categoryContainer {
  margin-bottom: 10px;
  width: 100%;
  flex: 1;
}

.bolusSettingsInnerContainer .categoryContainer:last-child {
  padding-right: 0;
}

.basalTable {
  composes: basalTable from './settings.css';
}

.basalTable td:first-child, .basalTable th:first-child {
  padding-left: 28px;
}

.settingsTable {
  composes: settingsTable from './settings.css';
}

.settingsTableInverted {
  composes: settingsTableInverted from './settings.css';
}

.settingsTable td:first-child, .settingsTable th:first-child,
.settingsTableInverted td:first-child, .settingsTableInverted th:first-child {
  padding-left: 28px;
}

.singleLineBasalScheduleHeader {
  composes: basalHeaderBackground settingsHeader from './settings.css';
  justify-content: space-between;
}

.automatedSingleLineBasalScheduleHeader {
  composes: automatedBasalHeaderBackground settingsHeader from './settings.css';
  justify-content: space-between;
  font-size: 16px;
  padding: 0;
}

.twoLineBasalScheduleHeader {
  composes: basalHeaderBackground settingsHeaderDecoration settingsHeaderLayout settingsHeaderText from './settings.css';
}

.twoLineBasalScheduleHeader > div:first-child {
  padding-left: 0;
}

.insulinSettingsHeader {
  composes: bolusHeaderBackground settingsHeader from './settings.css';
}

.presetSettingsHeader {
  composes: presetsHeaderBackground settingsHeader from './settings.css';
}

.bolusSettingsHeader {
  composes: bolusHeaderBackground settingsHeader from './settings.css';
}

.automatedBasalHeaderBackground {
  composes: automatedBasalHeaderBackground from './settings.css';
  text-align: left;
  padding-left: 28px;
  padding-top: 8px;
  padding-bottom: 8px
}

.automatedBasalHeaderBackground > span {
  padding-left: 5px;
}

@media print {

  .categoryContainer {
    max-width: 280px;
    min-width: 250px;
    width: 100%;
    margin-bottom: 10px;
    margin-right: 10px;
  }

  .settingsContainer {
    display: block;
  }

  .basalSettingsContainer {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    page-break-after: auto;
  }

  .categoryTitle {
    font-weight: normal;
    font-size: 20px;
    color: var(--text-black);
    width: 100%;
    padding-bottom: 10px;
  }
}
