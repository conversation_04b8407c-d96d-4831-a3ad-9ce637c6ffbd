/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2016, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

.secondaryLabelAlone {
  composes: secondaryLeftPadding from '../settings.css';
  font-weight: normal;
}

.secondaryLabelWithMain {
  composes: lightText from '../../../styles/typography.css';
  composes: secondaryLeftPadding from '../settings.css';
}

.tooltipIcon {
  position: relative;
  margin-right: .75em;
  float: right;
}

.tooltipIcon > img {
  width: 14px;
}

.rowTooltipIcon {
  composes: tooltipIcon;
  margin-right: 0;
  margin-left: .75em;
  float: none;
}

.rowTooltipIcon > img {
  width: 14px;
}

.TableTooltipWrapper {
  z-index: 1;
  position: relative;
}

@media print {

  .secondaryLabelWithMain {
    composes: lightText largeSize from '../../../styles/typography.css';
    color: var(--text-black);
  	composes: secondaryLeftPadding from '../settings.css';
  }
}
