/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2016, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

@import '../../styles/colors.css';

.header {
  display: flex;
  justify-content: space-between;

  align-items: center;
  align-content: center;
}

.headerCommon {
  line-height: 20px;
}

.basalScheduleHeader {
  composes: basalHeaderBackground settingsHeaderText from './settings.css';
  composes: headerCommon;
}

.bolusSettingsHeader {
  composes: bolusHeaderBackground settingsHeaderText from './settings.css';
  composes: headerCommon;
}

.collapsibleLabel {
  border-top: 1px solid var(--collapsible--border);
  justify-content: flex-start;
  line-height: 40px;
  margin-left: 20px;
  padding-left: 1em;
}

.profileTable {
  composes: basalTable from './settings.css';
  padding-bottom: 1em;
  margin-left: 40px;
  width: auto;
}

.profileTable td, .profileTable th {
  font-size: 14px;
  text-align: center;
}

.profileTable thead th {
  padding-left: 24px;
  padding-right: 10px;
}

.profileTable thead th:first-child {
  text-align: left;
  padding-left: 10px;
  padding-right: 10px;
}

.profileTable th:first-of-type {
  font-weight: bold;
}

.title {
  composes: boldText defaultSize mediumContrastText from '../../styles/typography.css';
  display: inline-block;
  padding-bottom: 10px;
}

.categoryTitle {
  composes: mediumContrastText from '../../styles/typography.css';
  width: 100%;
  padding-bottom: 10px;
  margin-left: 40px;
  margin-top: 15px;
}

.settingsTableInverted {
  composes: settingsTableInverted from './settings.css';
  margin-bottom: 1em;
  margin-left: 40px;
  width: 25%;
  min-width: 240px;
}

.settingsTableInverted td:first-child, .settingsTableInverted th:first-child {
  /* padding-left: 28px; */
  padding-left: 14px;
}

.insulinSettingsHeader {
  composes: bolusHeaderBackground settingsHeader from './settings.css';
}

.annotations {
  margin-left: 40px;
  margin-right: 40px;
  margin-top: 30px;
  font-size: 14px;
  font-weight: 500;
}

@media print {

  .title {
    font-weight: normal;
    font-size: 20px;
    color: var(--text-black);
    display: inline-block;
    padding-bottom: 10px;
  }

  .profileTable th {
    font-weight: normal;
    color: #000;
  }

  .profileTable th:first-of-type {
    font-weight: normal;
  }
}
