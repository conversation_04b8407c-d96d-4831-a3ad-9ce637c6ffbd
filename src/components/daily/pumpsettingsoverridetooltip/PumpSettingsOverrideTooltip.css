 @import '../../../styles/colors.css';

.container {
  opacity: 1;
  display: flex;
  flex-direction: column;
  min-width: 80px;
  margin: 2px 0;
  max-width: 250px;
}

.row {
  composes: smallSize from '../../../styles/typography.css';
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  line-height: 20px;
}

.label {
  flex-grow: 1;
}

.boldLabel {
  composes: label;
  font-weight: bold;
}

.value {
  margin-left: 20px;
}

.title {
  composes: smallSize from '../../../styles/typography.css';
  text-align: center;
}

.target {
  composes: row;
}

.overrideType {
  composes: row;
  font-weight: normal;
}

.overrideType div:last-child {
  font-weight: bold;
}
