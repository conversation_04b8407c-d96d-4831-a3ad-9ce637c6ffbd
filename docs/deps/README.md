## Dependencies

We use [webpack](https://webpack.github.io/ 'webpack module bundler') to bundle the JavaScript, CSS, and JSON assets in this repository into a single JavaScript bundle from which blip can import the pieces (components, functions, etc.) needed.

Some of the other dependencies we leverage in this repository are, like webpack, commonly used by many others building apps today with React, and some are a bit more unusual:

- [D3](https://d3js.org/ 'D3: Data-Driven Documents') ([read more](./D3.md))
- [GSAP](https://greensock.com/ 'GreenSock') ([read more](./GSAP.md))
- [Moment](http://momentjs.com/ 'Moment.js') ([read more](./Moment.md))
- [React](https://facebook.github.io/react/ 'React') ([read more](./React.md))
- [react-motion](https://github.com/chenglou/react-motion 'GitHub: react-motion') (see [D3 usage](./D3.md) as well as [React Motion](./ReactMotion.md))
- [webpack](https://webpack.github.io/ 'webpack module bundler') ([read more](./Webpack.md))

Follow the "read more" link where available in this list of tools to learn more about the specifics of our usage of the tool in this repository.
