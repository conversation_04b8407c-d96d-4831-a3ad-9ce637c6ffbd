## @tidepool/viz's usage of React

Aside from [our general views on React best practices at Tidepool](http://developer.tidepool.io/docs/front-end/react/index.html 'Tidepool developer portal: React @ Tidepool'), to develop code in this repository you should:

- understand our division between [components, "container" components, and views](../DirectoryStructure.md#react-component-directories)
- understand [how we use D3 and React together]('./D3.md')
