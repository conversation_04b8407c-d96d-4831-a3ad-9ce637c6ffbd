/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@browser-warning-zindex:       @zindex-overlay-background;
@browser-warning-text-zindex:  @zindex-overlay;
@browser-warning-box-width:    540px;

.browser-warning {
  background: #fff;
  margin: 30px auto;
  width: @browser-warning-box-width;
  overflow-y: auto;

  @media (max-width: @browser-warning-box-width) {
    width: 100%;
  }
}
.browser-warning-nowrap {
  white-space: nowrap;
}
.browser-warning-box {
  margin-right: auto;
  margin-left: auto;
  padding-left: @spacing-small;
  padding-right: @spacing-small;
  text-align: center;

  @media(min-width: @browser-warning-box-width) {
    width: @browser-warning-box-width;
  }

  .browser-warning-title {
    font-size: 24px;
    line-height: 32px;
    font-weight: 500;
    padding-left: @spacing-small;
    padding-right: @spacing-small;
    color: @gray-darker;
    text-align: center;
  }

  .browser-warning-text {
    text-align: center;
    line-height: 28px;

    .dark-text {
      color: @gray-darker;
      font-weight: 600;
    }
  }
  .blip-link-text-wrap {
    overflow: hidden;
    width: 1px;
    height: 1px;
  }
  .blip-link-text {
    border: none;
    margin: 0 5px;
    text-align: center;
    color: @gray-darker;
    width: 130px;
  }

  .browser-warning-copy-button {
    font-size: 16px;
    background-color: @purple;
    color: @white-color;
    font-weight: 400;
    padding: 6px 16px;
    border-radius: 8px;
    border: none;
    margin: 0 auto 20px;
    display: block;
  }

  .browser-warning-download-text {
    font-size: 14px;
    color: @gray-darker;
    font-weight: 500;
    line-height: 28px;

    a {
      color: @link-color;
    }
  }

  .browser-warning-chrome-image {
    background: url('./images/chrome-logo.svg') no-repeat;
    width: 100px;
    height: 100px;
    margin: 0 5px 16px auto;
    background-size: cover;
    display: inline-block;
  }

  .browser-warning-edge-image {
    background: url('./images/edge-logo.svg') no-repeat;
    width: 100px;
    height: 100px;
    margin: 0 auto 16px 5px;
    background-size: cover;
    display: inline-block;
  }

  .browser-warning-mobile {
    color: @gray-darker;
    text-align: center;
    border: solid 1px @gray-light;
    box-shadow: 0 6px 10px 4px rgba(60, 64, 67, 0.08);
    border-radius: 8px;
    margin: 16px;
    padding: 16px;
    line-height: 28px;

    .browser-warning-mobile-appstore-container {
      img {
        cursor: pointer;
        height: 40px;
        &.playstore-badge {
          height: 60px;
        }
      }
    }
  }

}
