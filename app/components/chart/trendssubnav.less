/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

#trendsSubNav {
  width: 100%;
  margin: auto;
  margin-top: 0;
  margin-bottom: 0;

  @media (min-width: @screen-lg-min) {
    padding-left: 0;
    padding-right: 0;
  }

  background-color: white;

  .trendsSubNavContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;

    @media (min-width: @screen-md-lg-min) {
      justify-content: space-between;
      padding-left: 30px;
    }
  }

  .domainLinks {
    padding: 0;
    margin: @spacing-small 0;
  }

  .dayFilters {
    padding: 0 @spacing-tiny;
    margin: @spacing-small 0;
  }

  .domainContainer {
    display: flex;
    justify-content: space-between;
    margin-top: @spacing-base;

    .active {
      font-weight: bold;
    }

    button {
      padding-left: @spacing-base;
      padding-right: @spacing-base;
    }

    @media (max-width: @screen-sm-max) {
      button {
        padding-left: @spacing-medium;
        padding-right: @spacing-medium;
      }
    }
  }

  .visibleDays {
    font-size: 12px;
    color: @gray-darkest;
    margin: 0 auto;
    text-align: center;
    padding-top: @spacing-tiny;
  }

  .daysGroupContainer {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin: @spacing-small 0;
  }

  .daysGroupContainer > div {
    margin: @spacing-small @spacing-tiny;
  }

  .daysGroup {
    margin-left: @spacing-tiny;
    margin-right: 2px;
    vertical-align: bottom;
  }

  a.dayFilter {
    cursor: pointer;
    display: inline-block;
    line-height: (1.5 * @spacing-base) - 4;
    padding: 0;
    width: 30px;
    margin: 0 3px;

    text-align: center;

    &.active {
      font-weight: bold;
      opacity: 1;
    }
  }
}
