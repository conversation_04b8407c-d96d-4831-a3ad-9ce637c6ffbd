.IncrementalInputArrow {
  display: block;
  line-height: 100%;
  font-size: 10px;

  path {
    fill: @gray-light;
  }
}

.IncrementalInputArrows {
  float: right;
  margin-left: @spacing-small;
}

.IncrementalInput {
  display: inline-block;
  padding: @spacing-tiny @spacing-tiny @spacing-tiny @spacing-small;
  border: solid 1px #bcbec0;
  margin: 0 @spacing-small;
  color: #58595b;
  background-color: #ffffff;
  user-select: none;

  &.IncrementalInput--error {
    border-color: @state-error-border;
  }

  &:hover {
    color: @purple-dark;
    border-color: @blue-green-light;

    .IncrementalInputArrow path {
      fill: #089eb3;

      &:hover {
        fill: @blue-green-light;
      }
    }
  }
}
