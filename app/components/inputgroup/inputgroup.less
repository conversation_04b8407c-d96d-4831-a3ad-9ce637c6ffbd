/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.input-group-error {
  .input-group-control {
    border-color: @red-error;
  }

  .input-group-message {
    color: @red-error;
  }
}

.input-group-checkbox-label,
.input-group-radio-label {
  padding-left: 20px;
  cursor: pointer;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-group-checkbox-control,
.input-group-radio-control {
  float: left;
  margin-left: -18px;
  margin-top: 2px;
}

.input-group textarea {
  resize: vertical;
}

.input-group-label {
  margin-bottom: 7px;
}

.input-group-explanation {
  margin: 15px -40px;
  text-align: center;
  font-weight: 300;
}

.input-group-control.placeholder {
  color: @gray-dark;
}

.input-group .Select {
  padding: 0;
  border: 0;

  .Select__control {
    min-height: 38px;
    width: 100%;
    float: left;

    &.Select__control--is-focused {
      border: 1px solid transparent;
    }
  }

  .Select__multi-value {
    border: 1px solid @blue-green;
    background-color: rgba(11, 158, 179, 0.08);

    .Select__multi-value__label {
      color: @blue-green;
    }

    .Select__multi-value__remove {
      border-left: 1px solid @blue-green;
      color: @blue-green;

      &:hover {
        background-color: rgba(0, 113, 230, 0.08);
        color: #0071e6;
      }
    }
  }
}
