import moment from 'moment-timezone';
import _ from 'lodash';
import get from 'lodash/get';
import { utils as vizUtils } from '@tidepool/viz';
import utils from '../../../../core/utils';

const getTimezoneFromTimePrefs = vizUtils.datetime.getTimezoneFromTimePrefs;

const sampleData = {
  'data': {
    'aggregationsByDate': {},
    'combined': [],
    'current': {},
    'next': {},
    'prev': {},
  },
  'timePrefs': {},
  'bgPrefs': {},
  'metaData': {
    'size': 17766,
    'bgSources': {
      'cbg': true,
      'smbg': true,
      'current': 'cbg',
    },
    'latestDatumByType': {
      'basal': {
        'deliveryType': 'automated',
        'deviceId': 'tandemCIQ1003717775089',
        'duration': 300000,
        'id': 'f1c02cb191dc06878e596e5b356606a2',
        'rate': 0.146,
        'revision': 1,
        'scheduleName': 'school',
        'suppressed': {
          'deliveryType': 'scheduled',
          'rate': 0.1,
          'type': 'basal',
          'duration': 300000,
          'time': 1751265720000,
          'normalTime': 1751265720000,
          'displayOffset': 0,
          'source': 'Unspecified Data Source',
          'normalEnd': 1751266020000,
          'subType': 'scheduled',
        },
        'time': 1751265720000,
        'type': 'basal',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        '_time': '2025-06-30T06:42:00Z',
        '_deviceTime': '2025-06-30T06:42:00Z',
        'deviceTime': 1751265720000,
        'tags': {
          'suspend': false,
          'temp': false,
        },
        'normalTime': 1751265720000,
        'displayOffset': 0,
        'deviceSerialNumber': '75079',
        'source': 'OneTouch',
        'normalEnd': 1751266020000,
        'subType': 'automated',
      },
      'bolus': {
        'deviceId': 'tandemCIQ1003717775089',
        'expectedNormal': 7,
        'id': 'f34fa2da7915ead69f2119def60351a0',
        'normal': 7,
        'revision': 1,
        'subType': 'normal',
        'time': 1751244420000,
        'type': 'bolus',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        '_time': '2025-06-30T00:47:00Z',
        '_deviceTime': '2025-06-30T00:47:00Z',
        'deviceTime': 1751244420000,
        'wizard': {
          'bgInput': 7.2123613006,
          'carbInput': 68,
          'carbUnits': 'grams',
          'deviceId': 'tandemCIQ1003717775089',
          'id': '22dd1d682ac6b2068ab526300227bb3b',
          'insulinCarbRatio': 10,
          'insulinSensitivity': 40.000014477,
          'revision': 1,
          'time': 1751244420000,
          'type': 'wizard',
          'units': 'mg/dL',
          'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
          '_time': '2025-06-30T00:47:00Z',
          '_deviceTime': '2025-06-30T00:47:00Z',
          'deviceTime': 1751244420000,
          'normalTime': 1751244420000,
          'displayOffset': 0,
          'deviceSerialNumber': '75079',
          'source': 'OneTouch',
          'bgNormalized': {
            'bgInput': true,
            'insulinSensitivity': true,
          },
        },
        'tags': {
          'automated': false,
          'correction': false,
          'extended': false,
          'interrupted': false,
          'manual': false,
          'override': false,
          'underride': false,
          'wizard': true,
          'loop': false,
          'oneButton': false,
        },
        'normalTime': 1751244420000,
        'displayOffset': 0,
        'deviceSerialNumber': '75079',
        'source': 'OneTouch',
      },
      'cbg': {
        'deviceId': 'tandemCIQ1003717775089',
        'deviceTime': 1751305320000,
        'id': '8aa20c5d10b80f161f1c1d35f93abbb2',
        'revision': 1,
        'time': 1751265720000,
        'type': 'cbg',
        'units': 'mg/dL',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        'value': 146.1206672161,
        'sampleInterval': 300000,
        '_time': '2025-06-30T06:42:00Z',
        '_deviceTime': '2025-06-30T10:42:00',
        'normalTime': 1751265720000,
        'displayOffset': 0,
        'warning': 'Combining `time` and `timezoneOffset` does not yield `deviceTime`.',
        'deviceSerialNumber': '75079',
        'source': 'OneTouch',
        'bgNormalized': {
          'value': true,
        },
        'msPer24': 24120000,
        'localDate': '2025-06-30',
      },
      'smbg': {
        'deviceId': 'OneTouchVerioIQ-TCF',
        'deviceTime': 1751197320000,
        'id': '090feb8a7911903336b47d79baf82c85',
        'revision': 1,
        'time': 1751157720000,
        'type': 'smbg',
        'units': 'mg/dL',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        'value': 237.99999606019998,
        '_time': '2025-06-29T00:42:00Z',
        '_deviceTime': '2025-06-29T04:42:00',
        'tags': {
          'manual': false,
          'meter': true,
        },
        'normalTime': 1751157720000,
        'displayOffset': 0,
        'warning': 'Combining `time` and `timezoneOffset` does not yield `deviceTime`.',
        'deviceSerialNumber': '75079',
        'source': 'OneTouch',
        'bgNormalized': {
          'value': true,
        },
        'msPer24': 2520000,
        'localDate': '2025-06-29',
      },
      'wizard': {
        'bgInput': 7.2123613006,
        'bolus': {
          'deviceId': 'tandemCIQ1003717775089',
          'expectedNormal': 7,
          'id': 'f34fa2da7915ead69f2119def60351a0',
          'normal': 7,
          'revision': 1,
          'subType': 'normal',
          'time': 1751244420000,
          'type': 'bolus',
          'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
          '_time': '2025-06-30T00:47:00Z',
          '_deviceTime': '2025-06-30T00:47:00Z',
          'deviceTime': 1751244420000,
          'normalTime': 1751244420000,
          'displayOffset': 0,
          'deviceSerialNumber': '75079',
          'source': 'OneTouch',
        },
        'carbInput': 68,
        'carbUnits': 'grams',
        'deviceId': 'tandemCIQ1003717775089',
        'id': '22dd1d682ac6b2068ab526300227bb3b',
        'insulinCarbRatio': 10,
        'insulinSensitivity': 40.000014477,
        'revision': 1,
        'time': 1751244420000,
        'type': 'wizard',
        'units': 'mg/dL',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        '_time': '2025-06-30T00:47:00Z',
        '_deviceTime': '2025-06-30T00:47:00Z',
        'deviceTime': 1751244420000,
        'normalTime': 1751244420000,
        'displayOffset': 0,
        'deviceSerialNumber': '75079',
        'source': 'OneTouch',
        'bgNormalized': {
          'bgInput': true,
          'insulinSensitivity': true,
        },
      },
      'upload': {
        '_dataState': 'open',
        '_deduplicator': {
          'name': 'org.tidepool.deduplicator.dataset.delete.origin',
          'version': '1.0.0',
        },
        '_state': 'closed',
        'byUser': '51263e09-799c-4be8-b03e-16c79dee76c7',
        'client': {
          'name': 'org.tidepool.uploader',
          'private': {
            'blobId': '80d7196599034b8e8bf6fdde8bc587e2',
            'os': 'darwin-x64-22.3.0',
          },
          'version': '2.52.0',
        },
        'computerTime': '2025-06-30T17:15:45',
        'conversionOffset': 0,
        'createdUserId': '51263e09-799c-4be8-b03e-16c79dee76c7',
        'dataSetType': 'normal',
        'deviceId': 'OneTouchUltra2-JNGZ162-T0111',
        'deviceManufacturers': [
          'OneTouch',
        ],
        'deviceModel': 'JNGZ162',
        'deviceSerialNumber': '75079',
        'deviceTags': [
          'bgm',
          'cgm',
          'insulin-pump',
        ],
        'deviceTime': 1751328945000,
        'id': '0bac5f9849e742ee1489d7d2e6ca5c37',
        'revision': 3,
        'time': 1751303745151,
        'timeProcessing': 'utc-bootstrapping',
        'timezone': 'US/Eastern',
        'timezoneOffset': -300,
        'type': 'upload',
        'uploadId': '0bac5f9849e742ee1489d7d2e6ca5c37',
        'version': '2.34.0',
        '_time': '2025-06-30T17:15:45.151Z',
        '_deviceTime': '2025-06-30T17:15:45',
        'normalTime': 1751285745151,
        'displayOffset': 0,
        'warning': 'Combining `time` and `timezoneOffset` does not yield `deviceTime`.',
        'source': 'OneTouch',
      },
    },
    'latestPumpUpload': {
      'deviceModel': 'JNGZ162',
      'isAutomatedBasalDevice': false,
      'isAutomatedBolusDevice': false,
      'isSettingsOverrideDevice': false,
      'manufacturer': 'onetouch',
    },
    'latestTimeZone': {
      'name': 'US/Eastern',
      'type': 'upload',
      'time': 1751303745151,
      'message': 'Defaulting to display in the timezone of most recent upload at 2025-06-30T13:15:45-04:00',
    },
    'patientId': 'e22b0ef4-6c2f-47e8-a7de-6e2c464bf1c7',
    'queryDataCount': 0,
  },
  'query': {},
  'fetchedUntil': '2025-05-31T00:00:00.000Z',
  'oneMinCgmFetchedUntil': null,
  'cacheUntil': 1751474538501,
};

const getOpts = (
  data = sampleData, // data from redux (state.blip.data)
  agpPeriodInDays,
) => {
  const getMostRecentDatumTimeByChartType = (data, _chartType) => {
    const getLatestDatums = types => _.pick(_.get(data, 'metaData.latestDatumByType'), types);

    let latestDatums = getLatestDatums(['cbg']) || [];

    return _.max(_.map(latestDatums, d => (d.normalEnd || d.normalTime)));
  };

  const mostRecentDatumDates = {
    agpCGM: getMostRecentDatumTimeByChartType(data, 'agpCGM'),
  };

  const timePrefs = (() => {
    const latestTimeZone = data?.metaData?.latestTimeZone;
    const queryParams = {};

    const localTimePrefs = utils.getTimePrefsForDataProcessing(latestTimeZone, queryParams);

    return localTimePrefs;
  })();

  const timezoneName = getTimezoneFromTimePrefs(timePrefs);

  const endOfToday = moment.utc().tz(timezoneName).endOf('day').subtract(1, 'ms');

  const setDateRangeToExtents = ({ startDate, endDate }) => ({
    startDate: startDate ? moment.utc(startDate).tz(timezoneName).startOf('day') : null,
    endDate: endDate ? moment.utc(endDate).tz(timezoneName).endOf('day').subtract(1, 'ms') : null,
  });

  const getLastNDays = (days, chartType) => {
    const endDate = get(mostRecentDatumDates, chartType)
      ? moment.utc(mostRecentDatumDates[chartType])
      : endOfToday;

    return setDateRangeToExtents({
      startDate: moment.utc(endDate).tz(timezoneName).subtract(days - 1, 'days'),
      endDate,
    });
  };

  const dates = getLastNDays(agpPeriodInDays, 'agpCGM');

  const offsetDates = {
    startDate: dates.startDate.clone().subtract(agpPeriodInDays, 'days'),
    endDate: dates.endDate.clone().subtract(agpPeriodInDays, 'days'),
  };

  const formatDateEndpoints = ({ startDate, endDate }) => (startDate && endDate ? [
    startDate.valueOf(),
    moment.utc(endDate).tz(timezoneName).add(1, 'day').startOf('day').valueOf(),
  ] : []);

  const opts = {
    agpCGM:       { disabled: false, endpoints: formatDateEndpoints(dates) },
    offsetAgpCGM: { disabled: false, endpoints: formatDateEndpoints(offsetDates) },
    agpBGM:       { disabled: true },
    basics:       { disabled: true },
    bgLog:        { disabled: true },
    daily:        { disabled: true },
    settings:     { disabled: true },
  };

  return opts;
};

export default getOpts;
