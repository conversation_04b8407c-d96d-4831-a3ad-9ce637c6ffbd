/* global expect */
/* global describe */
/* global afterEach */
/* global context */
/* global it */
/* global beforeEach */

import React from 'react';
import { render, screen } from '@testing-library/react';

import PeriodDeltaSummary from '@app/pages/dashboard/PatientDrawer/PeriodDeltaSummary';

const agpCGM = {
  timePrefs: {
    timezoneAware: true,
    timezoneName: 'Etc/GMT+7',
  },
  data: {
    current: {
      stats: {
        averageGlucose: {
          averageGlucose: 7.325994675523737,
          total: 3914,
        },
        bgExtents: {
          bgMax: 17.59587,
          bgMin: 2.16479,
          bgDaysWorn: 14,
          newestDatum: {
            deviceId: 'DexcomG7_327559618444f03f526f994a15ecee3dec34ecdd965210660916458ba7c040b8',
            deviceTime: 1750446603000,
            id: 'cc73e1d3754f41826df9d6fc2124e9f0',
            origin: {
              id: '24c19574-a876-5a0c-954f-68bd699d21dc',
            },
            payload: {
              displayDevice: 'iOS',
              displayTime: '2025-06-20T12:10:03.111-07:00',
              systemTime: '2025-06-20T19:10:03.111Z',
              transmitterGeneration: 'g7',
              transmitterId: '327559618444f03f526f994a15ecee3dec34ecdd965210660916458ba7c040b8',
              transmitterTicks: 828183,
              trend: 'flat',
              trendRate: 0.3,
              trendRateUnits: 'mg/dL/min',
            },
            time: 1750446603111,
            timezoneOffset: -420,
            trend: 'constant',
            trendRate: 0.01665,
            trendRateUnits: 'mmol/L/minute',
            type: 'cbg',
            units: 'mmol/L',
            uploadId: 'abe3097e7e21b5ab73dcc182f61187ec',
            value: 5.82829,
            sampleInterval: 300000,
            _time: '2025-06-20T19:10:03.111Z',
            _deviceTime: '2025-06-20T12:10:03',
            normalTime: 1750446603111,
            displayOffset: -420,
            deviceSerialNumber: 'Unknown',
            source: 'Dexcom',
            msPer24: 43803111,
            localDate: '2025-06-20',
          },
          oldestDatum: {
            deviceId: 'DexcomG7_c45ede3f52eceabb03655d0ba2a43c9041953768a48a5f59382197c3cb88465e',
            deviceTime: 1749279783000,
            id: '6caff0374d48e19a748f5679ce416cf7',
            origin: {
              id: '529ba8d8-b997-592e-a01f-b16d2bd5098e',
            },
            payload: {
              displayDevice: 'iOS',
              displayTime: '2025-06-07T00:03:03.720-07:00',
              systemTime: '2025-06-07T07:03:03.720Z',
              transmitterGeneration: 'g7',
              transmitterId: 'c45ede3f52eceabb03655d0ba2a43c9041953768a48a5f59382197c3cb88465e',
              transmitterTicks: 559082,
              trend: 'flat',
              trendRate: -0.4,
              trendRateUnits: 'mg/dL/min',
            },
            time: 1749279783720,
            timezoneOffset: -420,
            trend: 'constant',
            trendRate: -0.0222,
            trendRateUnits: 'mmol/L/minute',
            type: 'cbg',
            units: 'mmol/L',
            uploadId: 'abe3097e7e21b5ab73dcc182f61187ec',
            value: 8.49264,
            sampleInterval: 300000,
            _time: '2025-06-07T07:03:03.72Z',
            _deviceTime: '2025-06-07T00:03:03',
            normalTime: 1749279783720,
            displayOffset: -420,
            deviceSerialNumber: 'Unknown',
            source: 'Dexcom',
            msPer24: 183720,
            localDate: '2025-06-07',
          },
        },
        coefficientOfVariation: {
          coefficientOfVariation: 32.540647868186156,
          total: 3914,
        },
        glucoseManagementIndicator: {
          glucoseManagementIndicator: 6.467012224680735,
          glucoseManagementIndicatorAGP: 6.467012224680735,
          total: 3914,
        },
        sensorUsage: {
          sensorUsage: 1174200000,
          sensorUsageAGP: 100.60662142710261,
          total: 1209600000,
          sampleInterval: 300000,
          count: 3914,
        },
        timeInRange: {
          durations: {
            veryLow: 750536.5355135411,
            low: 1699744.5068983138,
            target: 72228104.24118549,
            high: 10264690.853346959,
            veryHigh: 1456923.8630556976,
            total: 1174200000,
          },
          counts: {
            veryLow: 34,
            low: 77,
            target: 3272,
            high: 465,
            veryHigh: 66,
            total: 3914,
          },
        },
      },
    },
  },
};

const offsetAgpCGM = {
  timePrefs: {
    timezoneAware: true,
    timezoneName: 'Etc/GMT+7',
  },
  'data': {
    'current': {
      'stats': {
        'averageGlucose': {
          'averageGlucose': 7.460624420125762,
          'total': 3975,
        },
        'bgExtents': {
          'bgMax': 18.09544,
          'bgMin': 2.16479,
          'bgDaysWorn': 14,
          'newestDatum': {
            'deviceId': 'DexcomG7_c45ede3f52eceabb03655d0ba2a43c9041953768a48a5f59382197c3cb88465e',
            'deviceTime': 1749279483000,
            'id': '262141b90d42219ad79b378cb12c7395',
            'origin': {
              'id': 'ecbeb5ca-a704-528a-9c77-14dd8c00a213',
            },
            'payload': {
              'displayDevice': 'iOS',
              'displayTime': '2025-06-06T23:58:03.453-07:00',
              'systemTime': '2025-06-07T06:58:03.453Z',
              'transmitterGeneration': 'g7',
              'transmitterId': 'c45ede3f52eceabb03655d0ba2a43c9041953768a48a5f59382197c3cb88465e',
              'transmitterTicks': 558782,
              'trend': 'flat',
              'trendRate': -0.2,
              'trendRateUnits': 'mg/dL/min',
            },
            'time': 1749279483453,
            'timezoneOffset': -420,
            'trend': 'constant',
            'trendRate': -0.0111,
            'trendRateUnits': 'mmol/L/minute',
            'type': 'cbg',
            'units': 'mmol/L',
            'uploadId': 'abe3097e7e21b5ab73dcc182f61187ec',
            'value': 8.77018,
            'sampleInterval': 300000,
            '_time': '2025-06-07T06:58:03.453Z',
            '_deviceTime': '2025-06-06T23:58:03',
            'normalTime': 1749279483453,
            'displayOffset': -420,
            'deviceSerialNumber': 'Unknown',
            'source': 'Dexcom',
            'msPer24': 86283453,
            'localDate': '2025-06-06',
          },
          'oldestDatum': {
            'deviceId': 'DexcomG7_30ccb745916dbc5d0c350c39287c882882b87a3dbceab9c44bde28f6f767b115',
            'deviceTime': 1748070291000,
            'id': 'ff5e94a69fe5c4aeb01f1f65b6133cbb',
            'origin': {
              'id': '23ec2734-07b3-510e-b23d-30448465504c',
            },
            'payload': {
              'displayDevice': 'iOS',
              'displayTime': '2025-05-24T00:04:51.821-07:00',
              'systemTime': '2025-05-24T07:04:51.821Z',
              'transmitterGeneration': 'g7',
              'transmitterId': '30ccb745916dbc5d0c350c39287c882882b87a3dbceab9c44bde28f6f767b115',
              'transmitterTicks': 219211,
              'trend': 'flat',
              'trendRate': -0.4,
              'trendRateUnits': 'mg/dL/min',
            },
            'time': 1748070291821,
            'timezoneOffset': -420,
            'trend': 'constant',
            'trendRate': -0.0222,
            'trendRateUnits': 'mmol/L/minute',
            'type': 'cbg',
            'units': 'mmol/L',
            'uploadId': 'abe3097e7e21b5ab73dcc182f61187ec',
            'value': 8.82569,
            'sampleInterval': 300000,
            '_time': '2025-05-24T07:04:51.821Z',
            '_deviceTime': '2025-05-24T00:04:51',
            'normalTime': 1748070291821,
            'displayOffset': -420,
            'deviceSerialNumber': 'Unknown',
            'source': 'Dexcom',
            'msPer24': 291821,
            'localDate': '2025-05-24',
          },
        },
        'coefficientOfVariation': {
          'coefficientOfVariation': 37.78298661066899,
          'total': 3975,
        },
        'glucoseManagementIndicator': {
          'glucoseManagementIndicator': 6.525028612671605,
          'glucoseManagementIndicatorAGP': 6.525028612671605,
          'total': 3975,
        },
        'sensorUsage': {
          'sensorUsage': **********,
          'sensorUsageAGP': 98.59119996031549,
          'total': 1209600000,
          'sampleInterval': 300000,
          'count': 3975,
        },
        'timeInRange': {
          'durations': {
            'veryLow': 782490.**********,
            'low': 2543094.**********,
            'target': 67555018.86792453,
            'high': 12780679.245283019,
            'veryHigh': 2738716.981132075,
            'total': **********,
          },
          'counts': {
            'veryLow': 36,
            'low': 117,
            'target': 3108,
            'high': 588,
            'veryHigh': 126,
            'total': 3975,
          },
        },
      },
    },
  },
};

describe('PatientDrawer/PeriodDeltaSummary', () => {
  describe('When data is not present', () => {
    it('renders nothing', () => {
      const { container } = render(<CGMStatistics agpCGM={undefined} />);

      expect(container).toBeEmptyDOMElement();
    });
  });

  describe('When data is in mg/dL', () => {
    it('renders the data in the expected format', () => {
      render(<CGMStatistics agpCGM={agpCGM} />);

      expect(screen.getByTestId('agp-table-time-range')).toHaveTextContent('Time RangeDecember 15, 2024 - January 13, 2025 (30 days)');
      expect(screen.getByTestId('agp-table-cgm-active')).toHaveTextContent('Time CGM Active99.9%');
      expect(screen.getByTestId('agp-table-avg-glucose')).toHaveTextContent('Average Glucose(Goal <154 mg/dL)121 mg/dL');
      expect(screen.getByTestId('agp-table-gmi')).toHaveTextContent('Glucose Management Indicator(Goal <7%)6.2%');
      expect(screen.getByTestId('agp-table-cov')).toHaveTextContent('Glucose Variability(Defined as a percent coefficient of variation. Goal <= 36%)49.8%');
    });
  });
});
